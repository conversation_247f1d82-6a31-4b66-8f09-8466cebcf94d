# 首页组件 (shouye)

RO百科网站的首页组件，为用户提供网站功能的导航入口和欢迎界面。

## ✨ 主要功能

- 🎨 **响应式设计** - 完美适配桌面端、平板和移动设备
- 🌙 **主题适配** - 自动适配明亮/暗黑主题
- ✨ **优雅动画** - 基于 framer-motion 的流畅进入动画
- 🎯 **功能导航** - 提供四大核心功能的快速访问入口
- 📱 **移动端优化** - 针对触摸设备进行了专门优化

## 🚀 组件结构

```
shouye/
├── shouye.jsx          # 主组件文件
├── shouyeyangshi.js    # 样式组件文件
├── index.js            # 模块导出文件
└── README.md           # 说明文档
```

## 📦 主要组件

### `Shouye`
主首页组件，包含完整的首页布局和功能。

**特性:**
- 自动适配主题
- 响应式布局
- 流畅的进入动画
- 功能卡片导航

### 样式组件
- `Shouyebeijingrongqi` - 首页背景容器
- `Zengqiangban_zhubiaoti` - 增强版主标题
- `Zengqiangban_gongnengkapian` - 增强版功能卡片
- 更多样式组件...

## 🎯 功能导航

首页提供四个主要功能的导航入口：

1. **怪物数据** (`/guaiwushuju`) - 查看详细的怪物信息
2. **物品数据** (`/wupinshuju`) - 浏览完整的物品资料库
3. **地图数据** (`/ditushuju`) - 探索游戏世界的地图信息
4. **技能数据** (`/jinengshuju`) - 学习各职业的技能详情

## 🔧 使用方法

### 基本使用

```javascript
import Shouye from './shouye';

function App() {
  return (
    <div>
      <Shouye />
    </div>
  );
}
```

### 在路由中使用

```javascript
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Shouye from './shouye';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Shouye />} />
        {/* 其他路由 */}
      </Routes>
    </BrowserRouter>
  );
}
```

## 🎨 主题适配

组件自动适配项目的主题系统：

- **明亮主题**: 温暖柔和的色调，清晰的对比度
- **暗黑主题**: 深色背景配合高亮元素，护眼舒适

## 📱 响应式特性

- **桌面端**: 网格布局，4列功能卡片
- **平板端**: 自适应网格，2-3列布局
- **移动端**: 单列布局，优化触摸体验

## 🔄 动画效果

- **页面进入**: 渐显动画，从上到下依次展现
- **卡片悬停**: 轻微上浮效果，增强交互反馈
- **点击反馈**: 缩放动画，提供即时反馈

## 🛠️ 技术栈

- React ^18.3.1
- styled-components ^6.1.19
- framer-motion ^12.23.12
- 项目主题系统

## 📄 许可证

MIT License
