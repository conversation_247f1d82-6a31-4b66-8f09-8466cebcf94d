import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Guanggaolunbo from './guanggaolunbo.jsx';
import Gonggaolan from './gonggaolan.jsx';

// 左侧组件容器
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled.div`
  position: fixed;
  top: 80px;
  left: 20px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 100;

  @media (max-width: 768px) {
    top: 70px;
    left: 10px;
    right: 10px;
    gap: 10px;
  }

  @media (max-width: 480px) {
    gap: 8px;
  }
`;

/**
 * 首页组件
 * 展示广告轮播组件和公告栏
 */
const Shouye = () => {
  const [pingmu_chicun, shezhi_pingmu_chicun] = useState({
    kuandu: window.innerWidth,
    gaodu: window.innerHeight
  });

  // 监听屏幕尺寸变化
  useEffect(() => {
    const chuli_chicun_bianhua = () => {
      shezhi_pingmu_chicun({
        kuandu: window.innerWidth,
        gaodu: window.innerHeight
      });
    };

    window.addEventListener('resize', chuli_chicun_bianhua);
    return () => window.removeEventListener('resize', chuli_chicun_bianhua);
  }, []);

  // 计算公告栏可用高度
  const jisuan_gonggaolan_gaodu = () => {
    const dingbu_gaodu = 80; // 顶部导航栏高度
    const guanggao_gaodu = 200; // 广告轮播大概高度
    const jianju_gaodu = 20; // 组件间距
    const dibu_liukong = 50; // 底部留空

    // 手机端调整
    if (pingmu_chicun.kuandu <= 768) {
      const shouji_dingbu = 70;
      const shouji_guanggao = 150;
      const shouji_jianju = 18;
      const shouji_dibu = 30;

      return pingmu_chicun.gaodu - shouji_dingbu - shouji_guanggao - shouji_jianju - shouji_dibu;
    }

    return pingmu_chicun.gaodu - dingbu_gaodu - guanggao_gaodu - jianju_gaodu - dibu_liukong;
  };

  return (
    <Zuocezujianrongqi>
      {/* 广告轮播组件 */}
      <Guanggaolunbo />

      {/* 公告栏组件 - 传入屏幕参数 */}
      <Gonggaolan
        pingmu_chicun={pingmu_chicun}
        keyong_gaodu={jisuan_gonggaolan_gaodu()}
      />
    </Zuocezujianrongqi>
  );
};

export default Shouye;
