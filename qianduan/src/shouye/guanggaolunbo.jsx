import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useShi<PERSON><PERSON><PERSON><PERSON><PERSON> } from '../zujian/minganbuju/zhutitiqigong.js';

// 广告轮播容器
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled(motion.div)`
  position: relative;
  width: auto;
  height: auto;
  max-width: 400px;
  max-height: 200px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 768px) {
    max-width: 100%;
    max-height: 180px;
  }

  @media (max-width: 480px) {
    max-height: 140px;
    border-radius: 6px;
  }
`;

// 广告图片
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled(motion.img)`
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
  border-radius: inherit;
`;

// 轮播指示器容器
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = styled.div`
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 6px;
  z-index: 2;
  padding: 6px 10px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);

  @media (max-width: 480px) {
    bottom: 8px;
    gap: 4px;
    padding: 4px 8px;
  }
`;

// 轮播指示器点
const Zhishiqidian = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'huoyue'
})`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.huoyue ? 'rgba(255, 255, 255, 0.9)' : 'rgba(255, 255, 255, 0.4)'};
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    background: ${props => props.huoyue ? 'rgba(255, 255, 255, 1)' : 'rgba(255, 255, 255, 0.6)'};
  }

  @media (max-width: 480px) {
    width: 6px;
    height: 6px;
  }
`;

// 图片切换动画配置
const tupian_donghua = {
  churu: {
    opacity: 0,
    scale: 1.1,
  },
  xianshi: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  },
  tuichu: {
    opacity: 0,
    scale: 0.9,
    transition: {
      duration: 0.3,
      ease: "easeIn"
    }
  }
};

/**
 * 广告轮播组件
 * 展示6张广告图片的轮播，点击跳转到指定链接
 */
const Guanggaolunbo = () => {
  const [dangqian_suoyin, shezhi_dangqian_suoyin] = useState(0);
  const { zhutidixiang } = useShiyongzhuti();
  
  // 广告图片数据 - 使用后端网关地址
  const guanggao_liebiao = [
    { tupian: 'http://127.0.0.1:8098/jiekou/ziyuanhuoqu/guanggao/guanggao1.png', lianjie: 'https://luoluo.blyfw.cn' },
    { tupian: 'http://127.0.0.1:8098/jiekou/ziyuanhuoqu/guanggao/guanggao2.png', lianjie: 'https://luoluo.blyfw.cn' },
    { tupian: 'http://127.0.0.1:8098/jiekou/ziyuanhuoqu/guanggao/guanggao3.png', lianjie: 'https://luoluo.blyfw.cn' },
    { tupian: 'http://127.0.0.1:8098/jiekou/ziyuanhuoqu/guanggao/guanggao4.png', lianjie: 'https://luoluo.blyfw.cn' },
    { tupian: 'http://127.0.0.1:8098/jiekou/ziyuanhuoqu/guanggao/guanggao5.png', lianjie: 'https://luoluo.blyfw.cn' },
    { tupian: 'http://127.0.0.1:8098/jiekou/ziyuanhuoqu/guanggao/guanggao6.png', lianjie: 'https://luoluo.blyfw.cn' }
  ];

  // 自动轮播效果
  useEffect(() => {
    const jiandingqi = setInterval(() => {
      shezhi_dangqian_suoyin(prev => (prev + 1) % guanggao_liebiao.length);
    }, 3000); // 每3秒切换一次

    return () => clearInterval(jiandingqi);
  }, [guanggao_liebiao.length]);

  // 处理图片点击事件
  const chuli_tupian_dianji = () => {
    const dangqian_guanggao = guanggao_liebiao[dangqian_suoyin];
    window.open(dangqian_guanggao.lianjie, '_blank');
  };

  // 处理指示器点击事件
  const chuli_zhishiqi_dianji = (suoyin, event) => {
    event.stopPropagation(); // 阻止事件冒泡
    shezhi_dangqian_suoyin(suoyin);
  };

  // 触摸开始位置
  const [chumo_kaishi_x, shezhi_chumo_kaishi_x] = useState(0);

  // 处理触摸开始
  const chuli_chumo_kaishi = (event) => {
    const chumo = event.touches[0];
    shezhi_chumo_kaishi_x(chumo.clientX);
  };

  // 处理触摸结束
  const chuli_chumo_jieshu = (event) => {
    const chumo = event.changedTouches[0];
    const chumo_jieshu_x = chumo.clientX;
    const huadong_juli = chumo_jieshu_x - chumo_kaishi_x;
    const huadong_julei = 50; // 滑动阈值

    if (Math.abs(huadong_juli) > huadong_julei) {
      if (huadong_juli > 0) {
        // 向右滑动，显示上一张
        shezhi_dangqian_suoyin(prev =>
          prev === 0 ? guanggao_liebiao.length - 1 : prev - 1
        );
      } else {
        // 向左滑动，显示下一张
        shezhi_dangqian_suoyin(prev =>
          (prev + 1) % guanggao_liebiao.length
        );
      }
    }
  };

  // 处理图片加载错误
  const chuli_tupian_cuowu = (event) => {
    console.log('广告图片加载失败:', event.target.src);
    // 可以设置默认图片或隐藏组件
  };

  return (
    <Guanggaolunborongqi
      onClick={chuli_tupian_dianji}
      onTouchStart={chuli_chumo_kaishi}
      onTouchEnd={chuli_chumo_jieshu}
    >
      <AnimatePresence mode="wait">
        <Guanggaotupian
          key={dangqian_suoyin}
          src={guanggao_liebiao[dangqian_suoyin].tupian}
          alt={`广告 ${dangqian_suoyin + 1}`}
          onError={chuli_tupian_cuowu}
          variants={tupian_donghua}
          initial="churu"
          animate="xianshi"
          exit="tuichu"
          theme={zhutidixiang}
        />
      </AnimatePresence>

      <Zhishiqirongqi>
        {guanggao_liebiao.map((_, suoyin) => (
          <Zhishiqidian
            key={suoyin}
            huoyue={suoyin === dangqian_suoyin}
            onClick={(event) => chuli_zhishiqi_dianji(suoyin, event)}
          />
        ))}
      </Zhishiqirongqi>
    </Guanggaolunborongqi>
  );
};

export default Guanggaolunbo;
