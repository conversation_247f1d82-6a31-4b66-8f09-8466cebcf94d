import { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useShiyon<PERSON><PERSON><PERSON><PERSON> } from '../zujian/minganbuju/zhutitiqigong.js';
import { useSize, useMemoizedFn, useUpdateEffect } from 'ahooks';

// 公告栏容器
const Gonggaolanrongqi = styled(motion.div)`
  position: relative;
  width: 400px;
  background: ${props => {
    // 暗黑主题使用更深的背景和更好的透明度
    if (props.theme.yanse.danjinse) {
      return `${props.theme.yanse.beijing_er}f0`; /* 暗黑主题更高透明度 */
    }
    return `${props.theme.yanse.beijing_er}e6`; /* 明亮主题保持原样 */
  }};
  border: 1px solid ${props => {
    // 暗黑主题使用更明显的边框
    if (props.theme.yanse.danjinse) {
      return props.theme.yanse.biankuang_qian || props.theme.yanse.biankuang;
    }
    return props.theme.yanse.biankuang;
  }};
  border-radius: ${props => props.theme.yuanjiao.zhong};
  box-shadow: ${props => {
    // 暗黑主题使用更强的阴影效果
    if (props.theme.yanse.danjinse) {
      return props.theme.yinying.chaoda || '0 20px 25px rgba(0, 0, 0, 0.4), 0 10px 10px rgba(0, 0, 0, 0.2)';
    }
    return props.theme.yinying.da;
  }};
  backdrop-filter: blur(12px);
  overflow: visible;

  @media (max-width: 768px) {
    width: 100%;
  }

  @media (max-width: 480px) {
    width: 100%;
  }
`;

// 公告栏标题
const Gonggaobiaoti = styled.div`
  background: linear-gradient(45deg, ${props => props.theme.yanse.zhuyao}, ${props => props.theme.yanse.zhuyao_jianbian});
  color: ${props => props.theme.yanse.wenzi_fanzhuan};
  padding: ${props => props.theme.jianju.zhongdeng};
  font-size: ${props => props.theme.ziti.daxiao.zhong};
  font-weight: ${props => props.theme.ziti.zhongliang.cu};
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.2);

  span {
    margin-left: 8px;
  }

  @media (max-width: 480px) {
    padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng};
    font-size: ${props => props.theme.ziti.daxiao.xiao};
  }
`;

// 公告内容容器
const Gonggaoneirong = styled(motion.div).withConfig({
  shouldForwardProp: (prop) => !['zhankai', 'zuida_gaodu', 'neirong_gaodu'].includes(prop)
})`
  padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.da} ${props => props.theme.jianju.da};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  line-height: 1.8;
  color: ${props => props.theme.yanse.wenzi_ciyao};
  overflow-y: ${props => props.zhankai ? 'auto' : 'hidden'};
  overflow-x: hidden;

  /* 动态高度：收起时使用传入的最大高度，展开时自动高度 */
  max-height: ${props => {
    if (props.zhankai) {
      return 'none';
    }
    // 收起状态：使用动态计算的最大高度
    return props.zuida_gaodu ? `${props.zuida_gaodu}px` : '200px';
  }};

  /* 平滑的高度过渡动画 */
  transition: max-height 0.5s ease-in-out;

  /* 自定义滚动条样式 - 适配明暗主题，带渐变动画效果 */
  &::-webkit-scrollbar {
    width: ${props => props.zhankai ? '6px' : '0px'};
    transition: width 0.4s ease;
  }

  &::-webkit-scrollbar-track {
    background: ${props => props.theme.yanse.beijing_er};
    border-radius: 3px;
    opacity: ${props => props.zhankai ? '1' : '0'};
    transition: opacity 0.4s ease;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => {
      // 检查是否为暗黑主题，使用淡金色
      if (props.theme.yanse.danjinse) {
        return props.theme.yanse.danjinse;
      }
      // 明亮主题使用边框色
      return props.theme.yanse.biankuang;
    }};
    border-radius: 3px;
    opacity: ${props => props.zhankai ? '1' : '0'};
    transition: all 0.4s ease;

    &:hover {
      background: ${props => {
        // 检查是否为暗黑主题，使用淡金色的悬停效果
        if (props.theme.yanse.danjinse_hou) {
          return props.theme.yanse.danjinse_hou;
        }
        // 明亮主题使用边框色的悬停效果
        return props.theme.yanse.biankuang_qian;
      }};
    }
  }

  /* Firefox滚动条样式 - 适配明暗主题，带动画效果 */
  scrollbar-width: ${props => props.zhankai ? 'thin' : 'none'};
  scrollbar-color: ${props => {
    if (!props.zhankai) return 'transparent transparent';
    // 检查是否为暗黑主题，使用淡金色
    if (props.theme.yanse.danjinse) {
      return `${props.theme.yanse.danjinse} ${props.theme.yanse.beijing_er}`;
    }
    // 明亮主题使用边框色
    return `${props.theme.yanse.biankuang} ${props.theme.yanse.beijing_er}`;
  }};

  @media (max-width: 768px) {
    padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng} ${props => props.theme.jianju.zhongdeng};
    font-size: ${props => props.theme.ziti.daxiao.xiaoxiao};
  }

  @media (max-width: 480px) {
    padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng};
    font-size: 12px;
    line-height: 1.6;
  }
`;

// 公告段落
const Gonggaoduanluo = styled.p`
  margin: 0 0 1em 0;

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

// 展开/收起按钮
const Zhankaiannui = styled(motion.button)`
  background-color: transparent;
  border: none;
  border-top: 1px solid ${props => props.theme.yanse.biankuang};
  color: ${props => props.theme.yanse.danjinse || props.theme.yanse.zhuyao};
  font-size: ${props => props.theme.ziti.daxiao.xiao};
  font-weight: ${props => props.theme.ziti.zhongliang.cu};
  cursor: pointer;
  padding: ${props => props.theme.jianju.xiao};
  text-align: center;
  width: 100%;
  transition: all 0.3s ease;

  -webkit-tap-highlight-color: transparent;
  user-select: none;
  outline: none;

  @media (hover: hover) {
    &:hover {
      background-color: ${props => {
        // 暗黑主题使用淡金色透明背景
        if (props.theme.yanse.danjinse_touming) {
          return props.theme.yanse.danjinse_touming;
        }
        // 明亮主题使用蓝色透明背景
        return props.theme.yanse.danlanse_touming || 'rgba(25, 118, 210, 0.1)';
      }};
      color: ${props => props.theme.yanse.danjinse || props.theme.yanse.zhuyao};
    }
  }

  &:focus {
    outline: none;
  }

  @media (max-width: 768px) {
    padding: 6px;
    font-size: 12px;
  }

  @media (max-width: 480px) {
    padding: 6px;
    font-size: 12px;
  }
`;



/**
 * 公告栏组件
 * 显示网站介绍和联系信息
 * @param {Object} pingmu_chicun - 屏幕尺寸 {kuandu, gaodu}
 * @param {number} keyong_gaodu - 公告栏可用高度
 */
const Gonggaolan = ({ pingmu_chicun, keyong_gaodu }) => {
  const [zhankai, shezhi_zhankai] = useState(false);
  const [xuyao_zhankai, shezhi_xuyao_zhankai] = useState(false);
  const [donghua_zhong, shezhi_donghua_zhong] = useState(false);
  const [neirong_gaodu, shezhi_neirong_gaodu] = useState(0);
  const [gundonghuidingbu_zhong, shezhi_gundonghuidingbu_zhong] = useState(false);
  const neirong_ref = useRef(null);
  const { zhutidixiang } = useShiyongzhuti();

  // 使用ahooks的useSize监听窗口大小变化
  const size = useSize(() => document.querySelector('body'));

  // 公告内容
  const gonggao_neirong = [
    "冒险岛小册子成立于2021年10月，是一个关于电脑端的网络游戏《冒险岛》（台服名称《新枫之谷》）的个人性综合攻略与数据库资料站, 如果喜欢我们，请记住我们的网址 mxd.dvg.cn",
    "资料逐步完善中，如果您有好的意见或发现数据错误，可以加入我们的交流群提供资料。",
    "冒险岛小册子QQ群：10086冒险岛小册子成立于2021年10月，是一个关于电脑端的网络游戏《冒险岛》（台服名称《新枫之谷》）的个人性综合攻略与数据库资料站, 如果喜欢我们，请记住我们的网址 mxd.dvg.cn",
    "资料逐步完善中，如果您有好的意见或发现数据错误，可以加入我们的交流群提供资料。",
    "冒险岛小册子QQ群：10086冒险岛小册子成立于2021年10月，是一个关于电脑端的网络游戏《冒险岛》（台服名称《新枫之谷》）的个人性综合攻略与数据库资料站, 如果喜欢我们，请记住我们的网址 mxd.dvg.cn",
    "资料逐步完善中，如果您有好的意见或发现数据错误，可以加入我们的交流群提供资料。",
    "冒险岛小册子QQ群：10086冒险岛小册子成立于2021年10月，是一个关于电脑端的网络游戏《冒险岛》（台服名称《新枫之谷》）的个人性综合攻略与数据库资料站, 如果喜欢我们，请记住我们的网址 mxd.dvg.cn",
    "资料逐步完善中，如果您有好的意见或发现数据错误，可以加入我们的交流群提供资料。",
    "冒险岛小册子QQ群：10086冒险岛小册子成立于2021年10月，是一个关于电脑端的网络游戏《冒险岛》（台服名称《新枫之谷》）的个人性综合攻略与数据库资料站, 如果喜欢我们，请记住我们的网址 mxd.dvg.cn",
    "资料逐步完善中，如果您有好的意见或发现数据错误，可以加入我们的交流群提供资料。",
    "冒险岛小册子QQ群：10086冒险岛小册子成立于2021年10月，是一个关于电脑端的网络游戏《冒险岛》（台服名称《新枫之谷》）的个人性综合攻略与数据库资料站, 如果喜欢我们，请记住我们的网址 mxd.dvg.cn",
    "资料逐步完善中，如果您有好的意见或发现数据错误，可以加入我们的交流群提供资料。",
    "冒险岛小册子QQ群：10086"
  ];

  // 根据可用高度动态计算最大显示高度
  const huoqu_zuida_gaodu = useMemoizedFn(() => {
    // 如果父组件传入了可用高度，使用传入的值
    if (keyong_gaodu && keyong_gaodu > 0) {
      // 预留一些空间给展开按钮和边距
      const anniu_gaodu = 30; // 展开按钮高度
      const biankuang_gaodu = 20; // 边框和边距
      return Math.max(100, keyong_gaodu - anniu_gaodu - biankuang_gaodu);
    }

    // 降级方案：基于屏幕尺寸的固定值
    const kuandu = pingmu_chicun?.kuandu || size?.width || window.innerWidth;
    const gaodu = pingmu_chicun?.gaodu || window.innerHeight;

    if (kuandu <= 480) {
      // 手机端：使用屏幕高度的30%
      return Math.max(150, gaodu * 0.3);
    } else if (kuandu <= 768) {
      // 平板端：使用屏幕高度的35%
      return Math.max(200, gaodu * 0.35);
    } else {
      // 电脑端：使用屏幕高度的40%
      return Math.max(250, gaodu * 0.4);
    }
  });

  // 获取真实行高
  const huoqu_zhenshixinggao = useMemoizedFn(() => {
    if (!neirong_ref.current) return 20; // 默认行高

    const yuansu = neirong_ref.current;
    const jisuanyang = getComputedStyle(yuansu);
    const xinggao = parseFloat(jisuanyang.lineHeight);

    // 如果lineHeight是normal或者无效值，使用fontSize * 1.8作为行高
    if (isNaN(xinggao) || jisuanyang.lineHeight === 'normal') {
      const zitidaxiao = parseFloat(jisuanyang.fontSize) || 14;
      return zitidaxiao * 1.8;
    }

    return xinggao;
  });

  // 不再需要基于行数的高度计算，直接使用动态高度

  // 不再需要复杂的行数计算，直接使用高度比较

  // 检查是否需要展开按钮
  const jianche_neirong_gaodu = useMemoizedFn(() => {
    if (neirong_ref.current) {
      const yuansu = neirong_ref.current;

      // 重置样式以获取真实高度
      const yuanyang_height = yuansu.style.height;
      const yuanyang_maxHeight = yuansu.style.maxHeight;
      yuansu.style.height = 'auto';
      yuansu.style.maxHeight = 'none';

      const zuida_gaodu = huoqu_zuida_gaodu();
      const zhenshigaodu = yuansu.scrollHeight;

      // 调试信息（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.log('公告栏高度计算:', {
          屏幕宽度: pingmu_chicun?.kuandu || size?.width || window.innerWidth,
          屏幕高度: pingmu_chicun?.gaodu || window.innerHeight,
          传入可用高度: keyong_gaodu,
          计算最大高度: zuida_gaodu,
          真实内容高度: zhenshigaodu,
          行高: huoqu_zhenshixinggao(),
          需要展开: zhenshigaodu > zuida_gaodu
        });
      }

      // 保存真实内容高度
      shezhi_neirong_gaodu(zhenshigaodu);

      // 恢复原样式
      yuansu.style.height = yuanyang_height;
      yuansu.style.maxHeight = yuanyang_maxHeight;

      // 基于高度判断是否需要展开按钮
      const xuyao = zhenshigaodu > zuida_gaodu;
      shezhi_xuyao_zhankai(xuyao);

      // 如果不需要展开按钮，确保收起状态
      if (!xuyao && zhankai) {
        shezhi_zhankai(false);
      }
    }
  });

  // 使用useUpdateEffect避免初始渲染时执行
  useUpdateEffect(() => {
    // 延迟检查，确保DOM已渲染和样式已应用
    const timer = setTimeout(jianche_neirong_gaodu, 150);
    return () => clearTimeout(timer);
  }, [size?.width, gonggao_neirong.length]);

  // 组件挂载后检查
  useEffect(() => {
    const timer = setTimeout(jianche_neirong_gaodu, 200);
    return () => clearTimeout(timer);
  }, []);

  // 滚动回顶部动画
  const gundonghuidingbu = useMemoizedFn(() => {
    return new Promise((resolve) => {
      if (!neirong_ref.current) {
        resolve();
        return;
      }

      const yuansu = neirong_ref.current;
      const qishi_weizhi = yuansu.scrollTop;

      // 如果已经在顶部，直接返回
      if (qishi_weizhi <= 5) {
        resolve();
        return;
      }

      shezhi_gundonghuidingbu_zhong(true);

      // 使用requestAnimationFrame实现平滑滚动
      const donghua_shichang = 300; // 300ms动画时长
      const kaishi_shijian = performance.now();

      const donghua_zhixing = (dangqian_shijian) => {
        const jindu = Math.min((dangqian_shijian - kaishi_shijian) / donghua_shichang, 1);

        // 使用easeOutCubic缓动函数
        const huandongjindu = 1 - Math.pow(1 - jindu, 3);

        yuansu.scrollTop = qishi_weizhi * (1 - huandongjindu);

        if (jindu < 1) {
          requestAnimationFrame(donghua_zhixing);
        } else {
          shezhi_gundonghuidingbu_zhong(false);
          resolve();
        }
      };

      requestAnimationFrame(donghua_zhixing);
    });
  });

  // 处理展开/收起
  const chuli_zhankai = useMemoizedFn(async (event) => {
    if (donghua_zhong || gundonghuidingbu_zhong) return;

    if (event.currentTarget) {
      event.currentTarget.blur();
    }

    // 如果是收起操作且不在顶部，先滚动回顶部
    if (zhankai && neirong_ref.current && neirong_ref.current.scrollTop > 5) {
      await gundonghuidingbu();
    }

    shezhi_donghua_zhong(true);
    shezhi_zhankai(prev => !prev);

    setTimeout(() => {
      shezhi_donghua_zhong(false);
    }, 500);
  });



  return (
    <Gonggaolanrongqi
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.5 }}
      theme={zhutidixiang}
    >
      <Gonggaobiaoti theme={zhutidixiang}>
        📢<span>公告栏</span>
      </Gonggaobiaoti>

      <Gonggaoneirong
        ref={neirong_ref}
        theme={zhutidixiang}
        zhankai={zhankai}
        zuida_gaodu={huoqu_zuida_gaodu()}
        neirong_gaodu={neirong_gaodu}
        initial={false}
        animate={{
          maxHeight: zhankai ?
            (size?.width <= 480 ? '200px' : '280px') :
            `${Math.ceil(huoqu_zuida_gaodu())}px`,
          opacity: 1
        }}
        transition={{
          duration: 0.5,
          ease: [0.25, 0.8, 0.25, 1],
          maxHeight: {
            duration: 0.4
          }
        }}
      >
        {gonggao_neirong.map((duanluo, suoyin) => (
          <Gonggaoduanluo key={suoyin}>
            {duanluo}
          </Gonggaoduanluo>
        ))}
      </Gonggaoneirong>

      {xuyao_zhankai && (
        <Zhankaiannui
          onClick={chuli_zhankai}
          theme={zhutidixiang}
          style={{
            opacity: xuyao_zhankai ? 1 : 0,
            transition: 'opacity 0.2s ease-in-out'
          }}
        >
          {zhankai ? '收起 ▲' : '展开更多 ▼'}
        </Zhankaiannui>
      )}
    </Gonggaolanrongqi>
  );
};

export default Gonggaolan;
