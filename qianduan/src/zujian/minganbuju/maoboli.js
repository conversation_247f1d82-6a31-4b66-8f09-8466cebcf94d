import React from 'react';
import styled from 'styled-components';

// 毛玻璃基础样式混合
const maobolijichu = `
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
`;

// 暗黑模式毛玻璃样式
const anheimaobolijichu = `
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
`;

// 毛玻璃卡片组件
export const Maobolikapian = styled.div`
  ${props => props.theme.mingcheng === 'anhei' ? anheimaobolijichu : maobolijichu}
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  padding: ${props => props.theme.jianju.zhongdeng};
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  position: relative;
  overflow: hidden;
  
  /* 悬停效果 */
  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.theme.mingcheng === 'anhei' 
      ? '0 12px 40px rgba(0, 0, 0, 0.4)' 
      : '0 12px 40px rgba(0, 0, 0, 0.15)'};
    background: ${props => props.theme.mingcheng === 'anhei' 
      ? 'rgba(255, 255, 255, 0.08)' 
      : 'rgba(255, 255, 255, 0.15)'};
  }
  
  /* 内部光晕效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.4) 50%, 
      transparent 100%
    );
    opacity: ${props => props.theme.mingcheng === 'anhei' ? '0.6' : '0.8'};
  }
  
  /* 尺寸变体 */
  ${props => props.daxiao === 'xiao' && `
    padding: ${props.theme.jianju.xiao};
    border-radius: ${props.theme.yuanjiao.xiao};
  `}
  
  ${props => props.daxiao === 'da' && `
    padding: ${props.theme.jianju.da};
    border-radius: ${props.theme.yuanjiao.da};
  `}
  
  /* 透明度变体 */
  ${props => props.touming === 'gao' && `
    background: ${props.theme.mingcheng === 'anhei' 
      ? 'rgba(255, 255, 255, 0.02)' 
      : 'rgba(255, 255, 255, 0.05)'};
  `}
  
  ${props => props.touming === 'di' && `
    background: ${props.theme.mingcheng === 'anhei' 
      ? 'rgba(255, 255, 255, 0.1)' 
      : 'rgba(255, 255, 255, 0.2)'};
  `}
`;

// 毛玻璃容器组件
export const Maobolirongqi = styled.div`
  ${props => props.theme.mingcheng === 'anhei' ? anheimaobolijichu : maobolijichu}
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  padding: ${props => props.theme.jianju.zhongdeng};
  position: relative;
  overflow: hidden;
  
  /* 全宽选项 */
  ${props => props.quankuan && `
    width: 100%;
  `}
  
  /* 居中选项 */
  ${props => props.juzhong && `
    margin: 0 auto;
  `}
`;

// 毛玻璃导航栏
export const Maobolidaohang = styled.nav`
  ${props => props.theme.mingcheng === 'anhei' ? anheimaobolijichu : maobolijichu}
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.jianju.zhongdeng};
  position: sticky;
  top: 20px;
  z-index: 100;
  
  /* 固定在顶部时的样式 */
  ${props => props.guding && `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    max-width: calc(100vw - 40px);
  `}
`;

// 毛玻璃按钮
export const Maobolianniu = styled.button`
  ${props => props.theme.mingcheng === 'anhei' ? anheimaobolijichu : maobolijichu}
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${props => props.theme.yuanjiao.xiao};
  padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  font-family: ${props => props.theme.ziti.jiazu};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  position: relative;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-1px);
    background: ${props => props.theme.mingcheng === 'anhei' 
      ? 'rgba(255, 255, 255, 0.1)' 
      : 'rgba(255, 255, 255, 0.2)'};
    box-shadow: ${props => props.theme.mingcheng === 'anhei' 
      ? '0 6px 20px rgba(0, 0, 0, 0.4)' 
      : '0 6px 20px rgba(0, 0, 0, 0.15)'};
  }
  
  &:active {
    transform: translateY(0);
  }
  
  /* 主要按钮样式 */
  ${props => props.leixing === 'zhuyao' && `
    background: linear-gradient(135deg, 
      ${props.theme.yanse.zhuyao}40, 
      ${props.theme.yanse.zhuyao_qian}40
    );
    border-color: ${props.theme.yanse.zhuyao}60;
    color: ${props.theme.yanse.wenzi_zhuyao};
    
    &:hover {
      background: linear-gradient(135deg, 
        ${props.theme.yanse.zhuyao}60, 
        ${props.theme.yanse.zhuyao_qian}60
      );
    }
  `}
`;

// 毛玻璃输入框
export const Maobolishurukuang = styled.input`
  ${props => props.theme.mingcheng === 'anhei' ? anheimaobolijichu : maobolijichu}
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: ${props => props.theme.yuanjiao.xiao};
  padding: ${props => props.theme.jianju.xiao} ${props => props.theme.jianju.zhongdeng};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  font-family: ${props => props.theme.ziti.jiazu};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  width: 100%;
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  
  &::placeholder {
    color: ${props => props.theme.yanse.wenzi_ciyao};
  }
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.yanse.zhuyao}80;
    background: ${props => props.theme.mingcheng === 'anhei' 
      ? 'rgba(255, 255, 255, 0.08)' 
      : 'rgba(255, 255, 255, 0.15)'};
    box-shadow: 0 0 0 3px ${props => props.theme.yanse.zhuyao}20;
  }
`;

// 毛玻璃模态框
export const Maobolimotaikuang = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${props => props.theme.jianju.zhongdeng};
  
  /* 背景遮罩 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.3);
  }
  
  /* 模态框内容 */
  > * {
    position: relative;
    z-index: 1;
    ${props => props.theme.mingcheng === 'anhei' ? anheimaobolijichu : maobolijichu}
    border-radius: ${props => props.theme.yuanjiao.da};
    padding: ${props => props.theme.jianju.da};
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
  }
`;

export default {
  Maobolikapian,
  Maobolirongqi,
  Maobolidaohang,
  Maobolianniu,
  Maobolishurukuang,
  Maobolimotaikuang,
};
