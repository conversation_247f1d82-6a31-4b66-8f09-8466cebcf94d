// 样式化组件 - 基于主题的预制组件

import styled from 'styled-components';
import { motion } from 'framer-motion';

// 主题容器
export const Zhutirognqi = styled.div`
  background-color: ${props => props.theme.yanse.beijing};
  color: ${props => props.theme.yanse.wenzi_zhuyao};
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  
  ${props => props.quankuan && `
    width: 100%;
  `}
  
  ${props => props.quangao && `
    height: 100vh;
  `}
  
  ${props => props.zhongxin && `
    display: flex;
    align-items: center;
    justify-content: center;
  `}
`;

// 主题表面（毛玻璃效果）
export const Zhutibiaomian = styled.div`
  /* 毛玻璃基础效果 */
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: ${props => props.theme.mingcheng === 'anhei'
    ? 'rgba(255, 255, 255, 0.05)'
    : 'rgba(255, 255, 255, 0.1)'};
  border: 1px solid ${props => props.theme.mingcheng === 'anhei'
    ? 'rgba(255, 255, 255, 0.1)'
    : 'rgba(255, 255, 255, 0.2)'};

  color: ${props => props.theme.yanse.wenzi_zhuyao};
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  padding: ${props => props.theme.jianju.zhongdeng};
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  position: relative;
  overflow: hidden;

  /* 内部光晕效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%
    );
    opacity: ${props => props.theme.mingcheng === 'anhei' ? '0.6' : '0.8'};
  }

  /* 默认阴影 */
  box-shadow: ${props => props.theme.mingcheng === 'anhei'
    ? '0 8px 32px rgba(0, 0, 0, 0.3)'
    : '0 8px 32px rgba(0, 0, 0, 0.1)'};

  ${props => props.xuanfu && `
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${props.theme.mingcheng === 'anhei'
        ? '0 12px 40px rgba(0, 0, 0, 0.4)'
        : '0 12px 40px rgba(0, 0, 0, 0.15)'};
      background: ${props.theme.mingcheng === 'anhei'
        ? 'rgba(255, 255, 255, 0.08)'
        : 'rgba(255, 255, 255, 0.15)'};
    }
  `}

  ${props => props.biankuang && `
    border: 1px solid ${props.theme.yanse.biankuang};
  `}
`;

// 主题文本
export const Zhutiwenben = styled.p`
  color: ${props => {
    if (props.leixing === 'zhuyao') return props.theme.yanse.wenzi_zhuyao;
    if (props.leixing === 'ciyao') return props.theme.yanse.wenzi_ciyao;
    if (props.leixing === 'jinzhi') return props.theme.yanse.wenzi_jinzhi;
    if (props.leixing === 'tishi') return props.theme.yanse.wenzi_tishi;
    return props.theme.yanse.wenzi_zhuyao;
  }};
  
  font-size: ${props => {
    if (props.daxiao === 'xiaoxiao') return props.theme.ziti.daxiao.xiaoxiao;
    if (props.daxiao === 'xiao') return props.theme.ziti.daxiao.xiao;
    if (props.daxiao === 'zhongdeng') return props.theme.ziti.daxiao.zhongdeng;
    if (props.daxiao === 'da') return props.theme.ziti.daxiao.da;
    if (props.daxiao === 'dada') return props.theme.ziti.daxiao.dada;
    if (props.daxiao === 'chaoda') return props.theme.ziti.daxiao.chaoda;
    return props.theme.ziti.daxiao.zhongdeng;
  }};
  
  font-weight: ${props => {
    if (props.zhongliang === 'xichang') return props.theme.ziti.zhongliang.xichang;
    if (props.zhongliang === 'putong') return props.theme.ziti.zhongliang.putong;
    if (props.zhongliang === 'zhongdeng') return props.theme.ziti.zhongliang.zhongdeng;
    if (props.zhongliang === 'cuhei') return props.theme.ziti.zhongliang.cuhei;
    if (props.zhongliang === 'hei') return props.theme.ziti.zhongliang.hei;
    return props.theme.ziti.zhongliang.putong;
  }};
  
  line-height: ${props => {
    if (props.xinggao === 'jinmi') return props.theme.ziti.xinggao.jinmi;
    if (props.xinggao === 'putong') return props.theme.ziti.xinggao.putong;
    if (props.xinggao === 'kuansong') return props.theme.ziti.xinggao.kuansong;
    return props.theme.ziti.xinggao.putong;
  }};
  
  margin: ${props => props.wubianju ? '0' : `0 0 ${props.theme.jianju.xiao} 0`};
  transition: color ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
`;

// 主题按钮（毛玻璃效果）
export const Zhutianniu = styled.button`
  /* 毛玻璃基础效果 */
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;

  /* 按钮类型样式 */
  background: ${props => {
    if (props.leixing === 'biankuang') {
      return props.theme.mingcheng === 'anhei'
        ? 'rgba(255, 255, 255, 0.05)'
        : 'rgba(255, 255, 255, 0.1)';
    }

    const baseColor = (() => {
      if (props.leixing === 'zhuyao') return props.theme.yanse.zhuyao;
      if (props.leixing === 'ciyao') return props.theme.yanse.ciyao;
      if (props.leixing === 'chenggong') return props.theme.yanse.chenggong;
      if (props.leixing === 'jinggao') return props.theme.yanse.jinggao;
      if (props.leixing === 'cuowu') return props.theme.yanse.cuowu;
      if (props.leixing === 'xinxi') return props.theme.yanse.xinxi;
      return props.theme.yanse.zhuyao;
    })();

    return `linear-gradient(135deg, ${baseColor}60, ${baseColor}40)`;
  }};

  border: 1px solid ${props => {
    if (props.leixing === 'biankuang') {
      return props.theme.mingcheng === 'anhei'
        ? 'rgba(255, 255, 255, 0.2)'
        : 'rgba(255, 255, 255, 0.3)';
    }

    const baseColor = (() => {
      if (props.leixing === 'zhuyao') return props.theme.yanse.zhuyao;
      if (props.leixing === 'ciyao') return props.theme.yanse.ciyao;
      if (props.leixing === 'chenggong') return props.theme.yanse.chenggong;
      if (props.leixing === 'jinggao') return props.theme.yanse.jinggao;
      if (props.leixing === 'cuowu') return props.theme.yanse.cuowu;
      if (props.leixing === 'xinxi') return props.theme.yanse.xinxi;
      return props.theme.yanse.zhuyao;
    })();

    return `${baseColor}80`;
  }};

  color: ${props => props.leixing === 'wenben' ? props.theme.yanse.wenzi_zhuyao : '#ffffff'};
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  padding: ${props => `${props.theme.jianju.xiao} ${props.theme.jianju.zhongdeng}`};
  font-size: ${props => props.theme.ziti.daxiao.zhongdeng};
  font-weight: ${props => props.theme.ziti.zhongliang.zhongdeng};
  font-family: ${props => props.theme.ziti.jiazu};
  cursor: pointer;
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};

  /* 内部光晕效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.6) 50%,
      transparent 100%
    );
    opacity: 0.8;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${props => props.theme.mingcheng === 'anhei'
      ? '0 6px 20px rgba(0, 0, 0, 0.4)'
      : '0 6px 20px rgba(0, 0, 0, 0.15)'};

    background: ${props => {
      if (props.leixing === 'biankuang') {
        return props.theme.mingcheng === 'anhei'
          ? 'rgba(255, 255, 255, 0.1)'
          : 'rgba(255, 255, 255, 0.2)';
      }

      const baseColor = (() => {
        if (props.leixing === 'zhuyao') return props.theme.yanse.zhuyao;
        if (props.leixing === 'ciyao') return props.theme.yanse.ciyao;
        if (props.leixing === 'chenggong') return props.theme.yanse.chenggong;
        if (props.leixing === 'jinggao') return props.theme.yanse.jinggao;
        if (props.leixing === 'cuowu') return props.theme.yanse.cuowu;
        if (props.leixing === 'xinxi') return props.theme.yanse.xinxi;
        return props.theme.yanse.zhuyao;
      })();

      return `linear-gradient(135deg, ${baseColor}80, ${baseColor}60)`;
    }};
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
  
  ${props => props.quankuan && `
    width: 100%;
  `}
  
  ${props => props.daxiao === 'xiao' && `
    padding: ${props.theme.jianju.xiaoxiao} ${props.theme.jianju.xiao};
    font-size: ${props.theme.ziti.daxiao.xiao};
  `}
  
  ${props => props.daxiao === 'da' && `
    padding: ${props.theme.jianju.zhongdeng} ${props.theme.jianju.da};
    font-size: ${props.theme.ziti.daxiao.da};
  `}
`;

// 主题框架
export const Zhutikuang = styled.div`
  border: 1px solid ${props => props.theme.yanse.biankuang};
  border-radius: ${props => props.theme.yuanjiao.zhongdeng};
  padding: ${props => props.theme.jianju.zhongdeng};
  background-color: ${props => props.theme.yanse.biaomian};
  transition: all ${props => props.theme.donghua.sujian.zhongdeng} ${props => props.theme.donghua.huanman.biaozhun};
  
  ${props => props.xuanfu && `
    &:hover {
      border-color: ${props.theme.yanse.biankuang_qian};
      box-shadow: ${props.theme.yinying.xiao};
    }
  `}
  
  ${props => props.yinying && `
    box-shadow: ${props.theme.yinying.xiao};
  `}
`;

// 动画容器
export const Donghuarongqi = styled(motion.div)`
  width: 100%;
  height: 100%;
`;

// 网格布局
export const Wanggebuju = styled.div`
  display: grid;
  gap: ${props => props.theme.jianju.zhongdeng};
  
  ${props => props.lie && `
    grid-template-columns: repeat(${props.lie}, 1fr);
  `}
  
  ${props => props.xiangying && `
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  `}
`;

// 弹性布局
export const Tanxingbuju = styled.div`
  display: flex;
  gap: ${props => props.theme.jianju.zhongdeng};
  
  ${props => props.fangxiang === 'lie' && `
    flex-direction: column;
  `}
  
  ${props => props.duiqi === 'zhongxin' && `
    align-items: center;
    justify-content: center;
  `}
  
  ${props => props.duiqi === 'kaishi' && `
    align-items: flex-start;
    justify-content: flex-start;
  `}
  
  ${props => props.duiqi === 'jieshu' && `
    align-items: flex-end;
    justify-content: flex-end;
  `}
  
  ${props => props.duiqi === 'fensan' && `
    justify-content: space-between;
  `}
  
  ${props => props.baoguo && `
    flex-wrap: wrap;
  `}
`;
